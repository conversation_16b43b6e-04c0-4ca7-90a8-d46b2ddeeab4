@import "tailwindcss";

@theme {
  /* Font Families */
  --font-family-serif: serif;
  --font-family-heading: var(--font-family-heading), Inter, "SF Pro Text", system-ui;
  --font-family-sans: var(--font-family-sans);
  --font-family-monospace: "SF Mono", ui-monospace, Monaco, Monospace;
  --font-family-notoSanJP: "Noto Sans JP", "Noto Sans JP", serif;

  /* Colors */
  --color-transparent: transparent;
  --color-white: #ffffff;
  --color-black: #000000;

  /* Primary Colors */
  --color-primary-50: #EDEFFF;
  --color-primary-100: #DFE3FF;
  --color-primary-200: #C2C8F9;
  --color-primary-300: #838FFB;
  --color-primary-400: #5D5AF9;
  --color-primary-500: #4C49F3;
  --color-primary-600: #4440DA;
  --color-primary-700: #3A37BE;
  --color-primary-800: #33309B;
  --color-primary-900: #2B2E66;

  /* Gray Colors */
  --color-gray-50: #F6F8FA;
  --color-gray-100: #EDF0F4;
  --color-gray-200: #E4E9EF;
  --color-gray-300: #D4DAE0;
  --color-gray-400: #9CA3AF;
  --color-gray-500: #6B7280;
  --color-gray-600: #474C5E;
  --color-gray-700: #313543;
  --color-gray-800: #272A35;
  --color-gray-900: #1D1E27;
  --color-gray-1000: #090A10;

  /* Red Colors */
  --color-red-50: #FEF2F2;
  --color-red-100: #FEE2E2;
  --color-red-300: #FCA5A5;
  --color-red-400: #F26F6F;
  --color-red-500: #EE5959;
  --color-red-600: #BE4747;
  --color-red-700: #8F3540;
  --color-red-800: #6B323C;
  --color-red-900: #542F3A;

  /* Green Colors */
  --color-green-50: #F0F9F5;
  --color-green-100: #E1F3EB;
  --color-green-300: #9AE0C0;
  --color-green-400: #7ACEA8;
  --color-green-500: #5CBB90;
  --color-green-600: #51A981;
  --color-green-800: #3A6F59;
  --color-green-900: #2D584B;

  /* Blue Colors */
  --color-blue-50: #EDF5FE;
  --color-blue-100: #E4EEFD;
  --color-blue-200: #C8DCFA;
  --color-blue-300: #ACC9FA;
  --color-blue-400: #81A8F4;
  --color-blue-500: #5081F0;
  --color-blue-600: #3263E9;
  --color-blue-800: #274199;
  --color-blue-900: #1E3A8A;
  --color-blue-1000: #DCF0F6;
  --color-blue-1100: #6CB1CD;
  --color-blue-1200: #2F505E;

  /* Pastel Green Colors */
  --color-pastelGreen-50: #E0F5DF;
  --color-pastelGreen-100: #DDF0D9;
  --color-pastelGreen-200: #CBE9C4;
  --color-pastelGreen-300: #B9E1B0;
  --color-pastelGreen-400: #A8DA9C;
  --color-pastelGreen-500: #84D57F;
  --color-pastelGreen-600: #7BAD6E;
  --color-pastelGreen-700: #5E8754;
  --color-pastelGreen-800: #42603A;
  --color-pastelGreen-900: #1B3039;
  --color-pastelGreen-1000: #396136;

  /* Orange Colors */
  --color-orange-50: #FFF7ED;
  --color-orange-100: #FFEDD5;
  --color-orange-500: #F08033;
  --color-orange-800: #814A28;
  --color-orange-900: #563B2F;

  /* Yellow Colors */
  --color-yellow-50: #FFFBEB;
  --color-yellow-100: #FEF3C7;
  --color-yellow-400: #FBCA4F;
  --color-yellow-500: #F2BA2C;
  --color-yellow-600: #C8971C;
  --color-yellow-800: #644C0E;
  --color-yellow-900: #503C0B;

  /* Purple Colors */
  --color-purple-50: #F5F3FF;
  --color-purple-100: #EDE9FE;
  --color-purple-500: #8B5CF6;
  --color-purple-600: #7B54D9;
  --color-purple-800: #50408D;
  --color-purple-900: #3A3667;

  /* Avatar Background Colors */
  --color-ava-bg-50: #F8EDED;
  --color-ava-bg-100: #F8EBE2;
  --color-ava-bg-200: #F8F3E2;
  --color-ava-bg-300: #EAF0E7;
  --color-ava-bg-400: #E1EFE9;
  --color-ava-bg-500: #E3EFF0;
  --color-ava-bg-600: #E4EEFD;
  --color-ava-bg-700: #DFE3FF;
  --color-ava-bg-800: #E7E4F8;
  --color-ava-bg-900: #F1E8FA;

  /* Avatar Text Colors */
  --color-ava-text-50: #482B2B;
  --color-ava-text-100: #483527;
  --color-ava-text-200: #4A4122;
  --color-ava-text-300: #273D1C;
  --color-ava-text-400: #1A372B;
  --color-ava-text-500: #1A3335;
  --color-ava-text-600: #21334D;
  --color-ava-text-700: #2A3267;
  --color-ava-text-800: #312862;
  --color-ava-text-900: #3B2453;

  /* Chart Colors */
  --color-chart-50: #996CFF;
  --color-chart-100: #5B8DFF;
  --color-chart-200: #52B3D0;
  --color-chart-300: #61D2AA;
  --color-chart-400: #F3CE70;
  --color-chart-500: #FFB185;
  --color-chart-600: #64CCED;
  --color-chart-700: #FA96BA;
  --color-chart-800: #89D067;
  --color-chart-900: #FF8D7D;
  --color-chart-1000: #FA9696;


  /* Breakpoints */
  --breakpoint-mobile: 360px;
  --breakpoint-tablet: 768px;
  --breakpoint-desktop: 1280px;
  --breakpoint-largeDesktop: 1600px;

  /* Custom Font Sizes */
  --font-size-6xs: 6px;
  --font-size-5xs: 8px;
  --font-size-4xs: 9px;
  --font-size-3xs: 10px;
  --font-size-2xs: 11px;
  --font-size-xs: 12px;
  --font-size-sm: 13px;
  --font-size-base: 15px;
  --font-size-lg: 17px;
  --font-size-xl: 19px;
  --font-size-2xl: 24px;
  --font-size-3xl: 29px;
  --font-size-4xl: 36px;
  --font-size-5xl: 45px;
  --font-size-6xl: 56px;
  --font-size-7xl: 70px;
  --font-size-8xl: 87px;
  --font-size-9xl: 109px;

  /* Custom Line Heights */
  --line-height-6xs: 6px;
  --line-height-5xs: 10px;
  --line-height-4xs: 12px;
  --line-height-3xs: 14px;
  --line-height-2xs: 16px;
  --line-height-xs: 18px;
  --line-height-sm: 20px;
  --line-height-base: 24px;
  --line-height-lg: 26px;
  --line-height-xl: 28px;
  --line-height-2xl: 32px;
  --line-height-3xl: 36px;
  --line-height-4xl: 40px;
  --line-height-5xl: 45px;
  --line-height-6xl: 56px;
  --line-height-7xl: 70px;
  --line-height-8xl: 87px;
  --line-height-9xl: 109px;

  /* Box Shadows */
  --shadow-textarea: 0px -5px 9px rgba(0, 0, 0, 0.05), 0px 1px 2px -1px rgba(0, 0, 0, 0.1);
  --shadow-ats: 0px 10px 15px -3px rgba(0, 0, 0, 0.1), 0px 4px 6px -2px rgba(0, 0, 0, 0.05), 0px 0px 0px 1px rgba(0, 0, 0, 0.05);
  --shadow-dark-ats: 0px 10px 15px -3px rgba(0, 0, 0, 0.15), 0px 4px 6px -2px rgba(0, 0, 0, 0.1), 0px 0px 0px 1px #313543;
  --shadow-dialog: 0px 1px 3px rgba(0, 0, 0, 0.1), 0px 1px 2px -1px rgba(0, 0, 0, 0.1);
  --shadow-select: 0px 0px 0px 1px #838FFB;
  --shadow-dark-select: 0px 0px 0px 1px #3A37BE;
  --shadow-error: 0px 0px 0px 1px #FCA5A5;
  --shadow-dark-error: 0px 0px 0px 1px #8F3540;
  --shadow-actions: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-editor: 0px 2px 4px -2px rgba(0, 0, 0, 0.10), 0px 4px 6px -1px rgba(0, 0, 0, 0.10);
  --shadow-sidebar: 7px 15px 17.6px -1px rgba(0, 0, 0, 0.05), 0px 2px 4px -2px rgba(0, 0, 0, 0.10);
  --shadow-actions-new: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
}

@layer base {
  * {
    box-sizing: border-box;
  }

  html {
    height: 100vh;
    -webkit-text-size-adjust: 100%;
  }

  body {
    height: 100vh;
    margin: 0;
    font-family: var(--font-family-sans);
    line-height: 1.5;
  }

  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentColor);
  }

  button:not(:disabled),
  [role="button"]:not(:disabled) {
    cursor: pointer;
  }

  button:disabled,
  [role="button"]:disabled {
    cursor: not-allowed;
  }

  dialog {
    margin: auto;
  }
}

/* Custom Utilities */
@layer utilities {
  .no-scrollbar {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
  }
  
  .no-scrollbar::-webkit-scrollbar {
    display: none; /* Chrome, Safari and Opera */
  }

  .appear {
    animation: makeVisible 0ms linear 200ms forwards;
  }

  .in {
    animation: enter 0.15s ease-out;
  }

  .from-left {
    animation: from-left 0.15s ease-out;
  }

  /* Custom Typography Utilities */
  .text-6xs {
    font-size: var(--font-size-6xs);
    line-height: var(--line-height-6xs);
  }

  .text-5xs {
    font-size: var(--font-size-5xs);
    line-height: var(--line-height-5xs);
  }

  .text-4xs {
    font-size: var(--font-size-4xs);
    line-height: var(--line-height-4xs);
  }

  .text-3xs {
    font-size: var(--font-size-3xs);
    line-height: var(--line-height-3xs);
  }

  .text-2xs {
    font-size: var(--font-size-2xs);
    line-height: var(--line-height-2xs);
  }

  .text-xs {
    font-size: var(--font-size-xs);
    line-height: var(--line-height-xs);
  }

  .text-sm {
    font-size: var(--font-size-sm);
    line-height: var(--line-height-sm);
  }

  .text-base {
    font-size: var(--font-size-base);
    line-height: var(--line-height-base);
  }

  .text-lg {
    font-size: var(--font-size-lg);
    line-height: var(--line-height-lg);
  }

  .text-xl {
    font-size: var(--font-size-xl);
    line-height: var(--line-height-xl);
  }

  .text-2xl {
    font-size: var(--font-size-2xl);
    line-height: var(--line-height-2xl);
  }

  .text-3xl {
    font-size: var(--font-size-3xl);
    line-height: var(--line-height-3xl);
  }

  .text-4xl {
    font-size: var(--font-size-4xl);
    line-height: var(--line-height-4xl);
  }

  .text-5xl {
    font-size: var(--font-size-5xl);
    line-height: var(--line-height-5xl);
  }

  .text-6xl {
    font-size: var(--font-size-6xl);
    line-height: var(--line-height-6xl);
  }

  .text-7xl {
    font-size: var(--font-size-7xl);
    line-height: var(--line-height-7xl);
  }

  .text-8xl {
    font-size: var(--font-size-8xl);
    line-height: var(--line-height-8xl);
  }

  .text-9xl {
    font-size: var(--font-size-9xl);
    line-height: var(--line-height-9xl);
  }
}

/* Keyframes */
@keyframes makeVisible {
  0% {
    visibility: hidden;
  }
  100% {
    visibility: visible;
  }
}

@keyframes enter {
  0% {
    opacity: 1;
    transform: translate3d(100%, 0, 0) scale3d(1, 1, 1) rotate(0);
  }
}

@keyframes from-left {
  0% {
    opacity: 1;
    transform: translate3d(-100%, 0, 0) scale3d(1, 1, 1) rotate(0);
  }
}

/* Component Styles */
@layer components {
  /* Typography Components */
  .prose {
    --tw-prose-body: var(--color-gray-900);
    --tw-prose-invert-body: var(--color-gray-200);
  }

  .prose ul {
    margin-top: 0;
    margin-bottom: 0;
  }

  .prose p {
    min-height: 1.5rem;
  }

  .prose-base {
    --tw-prose-body: var(--color-gray-900);
    --tw-prose-invert-body: var(--color-gray-200);
  }

  .prose-base ul {
    margin-top: 0;
    margin-bottom: 0;
  }

  .prose-base p {
    min-height: 1.5rem;
  }

  .prose-sm {
    --tw-prose-body: var(--color-gray-900);
    --tw-prose-invert-body: var(--color-gray-200);
  }

  .prose-sm ul {
    margin-top: 0;
    margin-bottom: 0;
  }

  .prose-sm p {
    min-height: 1.25rem;
  }

  /* Dark mode for prose */
  @media (prefers-color-scheme: dark) {
  }
}