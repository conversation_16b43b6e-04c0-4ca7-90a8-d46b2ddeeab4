import { cva } from 'class-variance-authority'
import Link from 'next/link'
import { FC, ReactNode } from 'react'
import { Badge } from '~/core/ui/Badge'
import If from '~/core/ui/If'
import { cn } from '~/core/ui/utils'

interface SidebarItemSettingsProps {
  icon?: ReactNode
  title?: string
  badge?: string
  selected?: boolean
  link?: string
  isDropdownMenu?: boolean
  noLink?: boolean
  onClick?: () => void
  premium?: boolean
}

const SidebarItemSettings: FC<SidebarItemSettingsProps> = ({
  icon,
  title,
  badge,
  selected,
  link = '',
  isDropdownMenu = false,
  noLink = false,
  onClick,
  premium = false
}) => {
  const linkVariants = cva(
    'group dark:bg-gray-900 flex items-center rounded-sm cursor-pointer font-medium whitespace-nowrap',
    {
      variants: {
        selected: {
          default:
            'text-gray-600 hover:text-gray-800 hover:[&>p]:text-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800',
          selected: 'bg-gray-100 dark:bg-gray-700 text-gray-800'
        },
        dropdown: {
          default: 'py-[6px] px-[10px]',
          dropdown: 'px-4 py-[10px]'
        }
      },
      defaultVariants: {
        selected: 'default',
        dropdown: 'default'
      }
    }
  )

  const renderHTML = () => {
    return (
      <>
        <If condition={icon}>
          <div className="mr-4 h-[18px] w-[18px]">{icon}</div>
        </If>
        <p
          className={cn(
            'text-sm font-medium',
            badge ? 'mr-2' : '',
            selected
              ? 'text-gray-900 dark:text-gray-300'
              : 'text-gray-700 dark:text-gray-300'
          )}>
          {title}
        </p>
        <If condition={badge}>
          <Badge color="red" radius="circular" size="sm">
            {badge}
          </Badge>
        </If>
        <If condition={premium}>
          <svg
            className="ml-1"
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 16 16"
            fill="none">
            <g clipPath="url(#clip0_18609_12981)">
              <path
                d="M12.0715 12.3584H3.97487C3.57615 12.3584 3.25293 12.6816 3.25293 13.0803C3.25293 13.4791 3.57615 13.8023 3.97487 13.8023H12.0715C12.4702 13.8023 12.7934 13.4791 12.7934 13.0803C12.7934 12.6816 12.4702 12.3584 12.0715 12.3584Z"
                fill="#F2BA2C"
              />
              <path
                d="M10.5493 8.01939L7.99537 3.72656L5.44141 8.01939L6.99397 11.5127H7.99537H12.2028L13.5768 6.24171L10.5493 8.01939Z"
                fill="#FCD77B"
              />
              <path
                d="M2.41406 6.24219L3.79584 11.5131H6.99412L5.44156 8.01987L2.41406 6.24219Z"
                fill="#FBCA4F"
              />
              <path
                d="M1.63819 6.06348C2.10121 6.06348 2.47657 5.68813 2.47657 5.2251C2.47657 4.76208 2.10121 4.38672 1.63819 4.38672C1.17516 4.38672 0.799805 4.76208 0.799805 5.2251C0.799805 5.68813 1.17516 6.06348 1.63819 6.06348Z"
                fill="#F2BA2C"
              />
              <path
                d="M7.99561 3.27637C8.45864 3.27637 8.83399 2.90102 8.83399 2.43799C8.83399 1.97497 8.45864 1.59961 7.99561 1.59961C7.53258 1.59961 7.15723 1.97497 7.15723 2.43799C7.15723 2.90102 7.53258 3.27637 7.99561 3.27637Z"
                fill="#F2BA2C"
              />
              <path
                d="M14.354 6.10938C14.817 6.10938 15.1924 5.73403 15.1924 5.271C15.1924 4.80797 14.817 4.43262 14.354 4.43262C13.891 4.43262 13.5156 4.80797 13.5156 5.271C13.5156 5.73403 13.891 6.10938 14.354 6.10938Z"
                fill="#F2BA2C"
              />
            </g>
            <defs>
              <clipPath id="clip0_18609_12981">
                <rect
                  width="14.4"
                  height="12.2031"
                  fill="white"
                  transform="translate(0.799805 1.59961)"
                />
              </clipPath>
            </defs>
          </svg>
        </If>
      </>
    )
  }

  if (noLink) {
    return (
      <div
        className={linkVariants({
          selected: selected ? 'selected' : 'default',
          dropdown: isDropdownMenu ? 'dropdown' : 'default'
        })}
        onClick={() => onClick && onClick()}>
        {renderHTML()}
      </div>
    )
  }

  return (
    <Link
      href={link}
      className={linkVariants({
        selected: selected ? 'selected' : 'default',
        dropdown: isDropdownMenu ? 'dropdown' : 'default'
      })}>
      {renderHTML()}
    </Link>
  )
}

export default SidebarItemSettings
