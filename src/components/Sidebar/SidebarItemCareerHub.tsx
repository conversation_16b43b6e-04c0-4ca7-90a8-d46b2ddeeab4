import { cva } from 'class-variance-authority'
import Link from 'next/link'
import { FC } from 'react'
import { cn } from '~/core/ui/utils'

interface SidebarItemCareerHubProps {
  title?: string
  selected?: boolean
  link?: string
}

const SidebarItemCareerHub: FC<SidebarItemCareerHubProps> = ({
  title,
  selected,
  link = ''
}) => {
  const linkVariants = cva(
    'group flex items-center rounded-sm cursor-pointer font-medium whitespace-nowrap',
    {
      variants: {
        selected: {
          default: 'text-gray-900 hover:text-primary-400',
          selected: 'text-primary-400'
        }
      },
      defaultVariants: {
        selected: 'default'
      }
    }
  )

  const renderHTML = () => {
    return (
      <>
        <p
          className={cn(
            'text-sm font-medium',
            selected
              ? 'text-primary-400 '
              : 'text-gray-900 hover:text-primary-400'
          )}>
          {title}
        </p>
      </>
    )
  }

  return (
    <Link
      href={link}
      className={linkVariants({
        selected: selected ? 'selected' : 'default'
      })}>
      {renderHTML()}
    </Link>
  )
}

export default SidebarItemCareerHub
