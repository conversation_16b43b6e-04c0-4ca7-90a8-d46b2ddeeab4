'use server'

import { getServerSession } from 'next-auth/next'
import { Manrope as HeadingFont, Inter as InterFont } from 'next/font/google'
import { cookies as cookiesHeader, headers } from 'next/headers'
import { redirect } from 'next/navigation'
import { LayoutProvider } from 'src/app/layoutApp'
import configuration from '~/configuration'
import { IUserInformation } from '~/core/@types/global'
import {
  SESSION_COOKIE_CURRENT_TENANT,
  SESSION_COOKIE_TENANTS,
  SESSION_COOKIE_USER
} from '~/core/constants/cookies'
import { NODE_ENV, PUBLIC_APP_URL } from '~/core/constants/env'
import { getUserAuthenticationServer } from '~/lib/next/with-auth'
import '../src/styles/globals.css'

const sans = InterFont({
  subsets: ['latin'],
  variable: '--font-family-sans',
  fallback: ['system-ui', 'Helvetica Neue', 'Helvetica', 'Arial'],
  preload: true,
  weight: ['300', '400', '500', '600', '700', '800'],
  display: 'swap'
})

const heading = HeadingFont({
  subsets: ['latin'],
  variable: '--font-family-heading',
  fallback: ['--font-family-sans'],
  preload: true,
  weight: ['400', '500'],
  display: 'swap'
})

export async function getUser() {
  const cookies = await cookiesHeader()
  const cookiesMapping = {
    [SESSION_COOKIE_USER]: String(cookies.get(SESSION_COOKIE_USER)?.value),
    [SESSION_COOKIE_TENANTS]: String(
      cookies.get(SESSION_COOKIE_TENANTS)?.value
    ),
    [SESSION_COOKIE_CURRENT_TENANT]: String(
      cookies.get(SESSION_COOKIE_CURRENT_TENANT)?.value
    )
  }

  return await getUserAuthenticationServer(cookiesMapping)
}

export async function getRedirect() {
  const header = await headers()
  const fullUrl = header.get('x-url') || ''
  const pathname = header.get('x-pathname') || '/'
  const searchParamsString = header.get('x-search-params') || ''
  const asPath = pathname + (searchParamsString ? `?${searchParamsString}` : '')
  const searchParams = new URLSearchParams(searchParamsString)
  const isCustomDomain =
    NODE_ENV === 'development'
      ? !['localhost:3000'].includes(header.get('host') as string)
      : !PUBLIC_APP_URL?.includes(header.get('host') as string)
  // Lấy từ Accept-Language header
  const acceptLanguage = header.get('accept-language') || ''
  const locale = acceptLanguage.split(',')[0]?.split('-')[0] // 'en', 'vi', etc.

  return {
    isCustomDomain,
    pathname,
    searchParams,
    asPath,
    routerLocale: locale, // need to test this - don't make sure i get correct value
    fullUrl
  }
}

export default async function RootLayout({
  children
}: Readonly<{
  children: React.ReactNode
}>) {
  const {
    isCustomDomain,
    pathname,
    searchParams,
    asPath,
    routerLocale,
    fullUrl
  } = await getRedirect()

  const isCareerPath = pathname?.startsWith(configuration.path.career.list)
  const is404Path = pathname?.startsWith(configuration.path.error404)
  if (isCustomDomain && !isCareerPath && !is404Path) {
    redirect(configuration.path.career.list)
  }

  const session = await getServerSession()
  const user = await getUser()

  return (
    <html
      translate="no"
      className={`light ${sans.variable} ${heading.variable}`}
      lang="en">
      <head>
        <link
          href="https://fonts.googleapis.com/css2?family=Noto+Sans+JP:wght@100..900&display=swap"
          rel="stylesheet"
        />
      </head>

      <body>
        <LayoutProvider
          data={{
            userInitialize: user as IUserInformation,
            session,
            pathname,
            searchParams,
            asPath,
            routerLocale,
            fullUrl
          }}>
          {children}
        </LayoutProvider>
      </body>
    </html>
  )
}
